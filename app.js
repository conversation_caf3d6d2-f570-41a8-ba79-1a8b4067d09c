require('dotenv').config();
require('./services/logger.js');
const express = require('express');
const path = require('path');
const engine = require('ejs-mate');
const os = require('os');
const multer = require('multer');
const fs = require('fs');
const csrf = require('csrf');
const { v4: uuidv4 } = require('uuid');
const session = require('express-session');
const bcrypt = require('bcrypt');
const { body, validationResult } = require('express-validator');
const rateLimit = require('express-rate-limit');
const User = require('./models/User');
const Subscription = require('./models/Subscription');
const Permission = require('./models/Permission');
const { db, checkIfUsersExist } = require('./db/database');
const systemMonitor = require('./services/systemMonitor');
const { uploadVideo } = require('./middleware/uploadMiddleware');
const QuotaMiddleware = require('./middleware/quotaMiddleware');
const { ensureDirectories } = require('./utils/storage');
const { getVideoInfo, generateThumbnail } = require('./utils/videoProcessor');

// Performance optimizations
const dbOptimizer = require('./db/optimizations');
const cacheService = require('./services/cacheService');
const staticOptimization = require('./middleware/staticOptimization');
const performanceMonitor = require('./services/performanceMonitor');
const Video = require('./models/Video');
const ffmpeg = require('fluent-ffmpeg');
const ffmpegInstaller = require('@ffmpeg-installer/ffmpeg');
const streamingService = require('./services/streamingService');
const schedulerService = require('./services/schedulerService');

// Import routes
const adminRoutes = require('./routes/admin');
const subscriptionRoutes = require('./routes/subscription');
ffmpeg.setFfmpegPath(ffmpegInstaller.path);
process.on('unhandledRejection', (reason, promise) => {
  console.error('-----------------------------------');
  console.error('UNHANDLED REJECTION AT:', promise);
  console.error('REASON:', reason);
  console.error('-----------------------------------');
});
process.on('uncaughtException', (error) => {
  console.error('-----------------------------------');
  console.error('UNCAUGHT EXCEPTION:', error);
  console.error('-----------------------------------');
});
const app = express();
const port = process.env.PORT || 7575;
const tokens = new csrf();

// Database migration function
async function migrateDatabase() {
  return new Promise((resolve, reject) => {
    console.log('Checking database schema...');

    // Add missing columns to videos table
    db.run(`ALTER TABLE videos ADD COLUMN created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP`, (err) => {
      if (err && !err.message.includes('duplicate column name')) {
        console.error('Error adding created_at to videos:', err.message);
      }
    });

    db.run(`ALTER TABLE videos ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP`, (err) => {
      if (err && !err.message.includes('duplicate column name')) {
        console.error('Error adding updated_at to videos:', err.message);
      }
    });

    // Add missing columns to users table
    db.run(`ALTER TABLE users ADD COLUMN email TEXT UNIQUE`, (err) => {
      if (err && !err.message.includes('duplicate column name')) {
        console.error('Error adding email to users:', err.message);
      }
    });

    db.run(`ALTER TABLE users ADD COLUMN role TEXT DEFAULT "user"`, (err) => {
      if (err && !err.message.includes('duplicate column name')) {
        console.error('Error adding role to users:', err.message);
      }
    });

    db.run(`ALTER TABLE users ADD COLUMN plan_type TEXT DEFAULT "free"`, (err) => {
      if (err && !err.message.includes('duplicate column name')) {
        console.error('Error adding plan_type to users:', err.message);
      }
    });

    db.run(`ALTER TABLE users ADD COLUMN max_streaming_slots INTEGER DEFAULT 1`, (err) => {
      if (err && !err.message.includes('duplicate column name')) {
        console.error('Error adding max_streaming_slots to users:', err.message);
      }
    });

    db.run(`ALTER TABLE users ADD COLUMN max_storage_gb INTEGER DEFAULT 5`, (err) => {
      if (err && !err.message.includes('duplicate column name')) {
        console.error('Error adding max_storage_gb to users:', err.message);
      }
    });

    db.run(`ALTER TABLE users ADD COLUMN used_storage_gb REAL DEFAULT 0`, (err) => {
      if (err && !err.message.includes('duplicate column name')) {
        console.error('Error adding used_storage_gb to users:', err.message);
      }
    });

    db.run(`ALTER TABLE users ADD COLUMN is_active BOOLEAN DEFAULT 1`, (err) => {
      if (err && !err.message.includes('duplicate column name')) {
        console.error('Error adding is_active to users:', err.message);
      }
    });

    db.run(`ALTER TABLE users ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP`, (err) => {
      if (err && !err.message.includes('duplicate column name')) {
        console.error('Error adding updated_at to users:', err.message);
      }
    });

    // Add missing columns to streams table
    db.run(`ALTER TABLE streams ADD COLUMN user_id TEXT`, (err) => {
      if (err && !err.message.includes('duplicate column name')) {
        console.error('Error adding user_id to streams:', err.message);
      }
    });

    db.run(`ALTER TABLE streams ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP`, (err) => {
      if (err && !err.message.includes('duplicate column name')) {
        console.error('Error adding updated_at to streams:', err.message);
      }
    });

    db.run(`ALTER TABLE streams ADD COLUMN orientation TEXT DEFAULT 'horizontal'`, (err) => {
      if (err && !err.message.includes('duplicate column name')) {
        console.error('Error adding orientation to streams:', err.message);
      }
    });

    db.run(`ALTER TABLE streams ADD COLUMN loop_video BOOLEAN DEFAULT 1`, (err) => {
      if (err && !err.message.includes('duplicate column name')) {
        console.error('Error adding loop_video to streams:', err.message);
      }
    });

    db.run(`ALTER TABLE streams ADD COLUMN status_updated_at TIMESTAMP`, (err) => {
      if (err && !err.message.includes('duplicate column name')) {
        console.error('Error adding status_updated_at to streams:', err.message);
      }
    });

    db.run(`ALTER TABLE streams ADD COLUMN start_time TIMESTAMP`, (err) => {
      if (err && !err.message.includes('duplicate column name')) {
        console.error('Error adding start_time to streams:', err.message);
      }
    });

    db.run(`ALTER TABLE streams ADD COLUMN end_time TIMESTAMP`, (err) => {
      if (err && !err.message.includes('duplicate column name')) {
        console.error('Error adding end_time to streams:', err.message);
      }
    });

    db.run(`ALTER TABLE streams ADD COLUMN duration INTEGER`, (err) => {
      if (err && !err.message.includes('duplicate column name')) {
        console.error('Error adding duration to streams:', err.message);
      }
    });

    // Add missing columns to stream_history table
    db.run(`ALTER TABLE stream_history ADD COLUMN user_id TEXT`, (err) => {
      if (err && !err.message.includes('duplicate column name')) {
        console.error('Error adding user_id to stream_history:', err.message);
      }

      // Update existing records
      setTimeout(() => {
        // Update videos without created_at
        db.run(`UPDATE videos SET created_at = CURRENT_TIMESTAMP WHERE created_at IS NULL`);
        db.run(`UPDATE users SET updated_at = CURRENT_TIMESTAMP WHERE updated_at IS NULL`);

        console.log('✅ Database migration completed');
        resolve();
      }, 1000);
    });
  });
}

// Run migration before starting the app
migrateDatabase().then(() => {
  console.log('Database migration completed successfully');
}).catch((error) => {
  console.error('Database migration failed:', error);
});

ensureDirectories();
ensureDirectories();
app.locals.helpers = {
  getUsername: function (req) {
    if (req.session && req.session.username) {
      return req.session.username;
    }
    return 'User';
  },
  getAvatar: function (req) {
    if (req.session && req.session.userId) {
      const avatarPath = req.session.avatar_path;
      if (avatarPath) {
        return `<img src="${avatarPath}" alt="${req.session.username || 'User'}'s Profile" class="w-full h-full object-cover" onerror="this.onerror=null; this.src='/images/default-avatar.jpg';">`;
      }
    }
    return '<img src="/images/default-avatar.jpg" alt="Default Profile" class="w-full h-full object-cover">';
  },
  getPlatformIcon: function (platform) {
    switch (platform) {
      case 'YouTube': return 'youtube';
      case 'Facebook': return 'facebook';
      case 'Twitch': return 'twitch';
      case 'TikTok': return 'tiktok';
      case 'Instagram': return 'instagram';
      case 'Shopee Live': return 'shopping-bag';
      case 'Restream.io': return 'live-photo';
      default: return 'broadcast';
    }
  },
  getPlatformColor: function (platform) {
    switch (platform) {
      case 'YouTube': return 'red-500';
      case 'Facebook': return 'blue-500';
      case 'Twitch': return 'purple-500';
      case 'TikTok': return 'gray-100';
      case 'Instagram': return 'pink-500';
      case 'Shopee Live': return 'orange-500';
      case 'Restream.io': return 'teal-500';
      default: return 'gray-400';
    }
  },
  formatDateTime: function (isoString) {
    if (!isoString) return '--';
    const date = new Date(isoString);
    return date.toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  },
  formatDuration: function (seconds) {
    if (!seconds) return '--';
    const hours = Math.floor(seconds / 3600).toString().padStart(2, '0');
    const minutes = Math.floor((seconds % 3600) / 60).toString().padStart(2, '0');
    const secs = Math.floor(seconds % 60).toString().padStart(2, '0');
    return `${hours}:${minutes}:${secs}`;
  },
  getUserRole: function (req) {
    return req.session.userRole || 'user';
  }
};
app.use(session({
  secret: process.env.SESSION_SECRET,
  resave: false,
  saveUninitialized: false,
  cookie: {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    maxAge: 24 * 60 * 60 * 1000
  }
}));
app.use(async (req, res, next) => {
  if (req.session && req.session.userId) {
    try {
      const user = await User.findById(req.session.userId);
      if (user) {
        req.session.username = user.username;
        req.session.avatar_path = user.avatar_path;
        req.session.userRole = user.role || 'user';
        if (user.email) req.session.email = user.email;
        res.locals.user = {
          id: user.id,
          username: user.username,
          avatar_path: user.avatar_path,
          email: user.email,
          role: user.role
        };
      }
    } catch (error) {
      console.error('Error loading user:', error);
      // Don't fail the request, just continue without user data
      // This prevents the cache service errors from breaking the app
    }
  }
  res.locals.req = req;
  next();
});
app.use(function (req, res, next) {
  if (!req.session.csrfSecret) {
    req.session.csrfSecret = uuidv4();
  }
  res.locals.csrfToken = tokens.create(req.session.csrfSecret);
  next();
});
app.engine('ejs', engine);
app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'views'));
app.use(express.static(path.join(__dirname, 'public')));
app.use('/uploads', function (req, res, next) {
  res.header('Cache-Control', 'no-cache');
  res.header('Pragma', 'no-cache');
  res.header('Expires', '0');
  next();
});
app.use(express.urlencoded({ extended: true }));
app.use(express.json());

// Performance monitoring middleware
app.use(performanceMonitor.requestTracker());

// Static file optimization middleware
app.use(staticOptimization.middleware());
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    const dir = './public/uploads/avatars';
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    cb(null, dir);
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = file.originalname.split('.').pop();
    cb(null, 'avatar-' + uniqueSuffix + '.' + ext);
  }
});
const upload = multer({
  storage: storage,
  limits: { fileSize: 5 * 1024 * 1024 },
  fileFilter: function (req, file, cb) {
    if (!file.originalname.match(/\.(jpg|jpeg|png|gif)$/)) {
      return cb(new Error('Only image files are allowed!'), false);
    }
    cb(null, true);
  }
});
const videoStorage = multer.diskStorage({
  destination: function (req, file, cb) {
    cb(null, path.join(__dirname, 'public', 'uploads', 'videos'));
  },
  filename: function (req, file, cb) {
    const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
    const ext = path.extname(file.originalname);
    let fileName = `video-${uniqueSuffix}${ext}`;
    let fullPath = path.join(__dirname, 'public', 'uploads', 'videos', fileName);
    let counter = 1;
    while (fs.existsSync(fullPath)) {
      fileName = `video-${uniqueSuffix}-${counter}${ext}`;
      fullPath = path.join(__dirname, 'public', 'uploads', 'videos', fileName);
      counter++;
    }
    cb(null, fileName);
  }
});
const videoUpload = multer({
  storage: videoStorage,
  fileFilter: function (req, file, cb) {
    if (!file.mimetype.match(/^video\/(mp4|avi|quicktime)$/)) {
      return cb(new Error('Only MP4, AVI, and MOV video files are allowed!'), false);
    }
    cb(null, true);
  }
});
const csrfProtection = function (req, res, next) {
  if ((req.path === '/login' && req.method === 'POST') ||
    (req.path === '/setup-account' && req.method === 'POST')) {
    return next();
  }
  const token = req.body._csrf || req.query._csrf || req.headers['x-csrf-token'];
  if (!token || !tokens.verify(req.session.csrfSecret, token)) {
    return res.status(403).render('error', {
      title: 'Error',
      error: 'CSRF validation failed. Please try again.'
    });
  }
  next();
};
const isAuthenticated = (req, res, next) => {
  if (req.session.userId) {
    return next();
  }
  res.redirect('/login');
};

// Enhanced authentication middleware with quota checking
const isAuthenticatedWithQuota = [
  isAuthenticated,
  QuotaMiddleware.checkActiveAccount()
];
app.use('/uploads', function (req, res, next) {
  res.header('Cache-Control', 'no-cache');
  res.header('Pragma', 'no-cache');
  res.header('Expires', '0');
  next();
});
app.use('/uploads/avatars', (req, res, next) => {
  const file = path.join(__dirname, 'public', 'uploads', 'avatars', path.basename(req.path));
  if (fs.existsSync(file)) {
    const ext = path.extname(file).toLowerCase();
    let contentType = 'application/octet-stream';
    if (ext === '.jpg' || ext === '.jpeg') contentType = 'image/jpeg';
    else if (ext === '.png') contentType = 'image/png';
    else if (ext === '.gif') contentType = 'image/gif';
    res.header('Content-Type', contentType);
    res.header('Cache-Control', 'max-age=60, must-revalidate');
    fs.createReadStream(file).pipe(res);
  } else {
    next();
  }
});
const loginLimiter = rateLimit({
  windowMs: 15 * 60 * 1000,
  max: 5,
  standardHeaders: true,
  legacyHeaders: false,
  // Fix for undefined IP address error
  keyGenerator: (req) => {
    // Safely get IP address with fallback
    const ip = req.ip || req.connection?.remoteAddress || req.socket?.remoteAddress || 'unknown';
    return ip;
  },
  // Skip rate limiting if IP cannot be determined
  skip: (req) => {
    const ip = req.ip || req.connection?.remoteAddress || req.socket?.remoteAddress;
    return !ip || ip === 'unknown';
  },
  handler: (req, res) => {
    res.status(429).render('login', {
      title: 'Login',
      error: 'Too many login attempts. Please try again in 15 minutes.'
    });
  },
  requestWasSuccessful: (request, response) => {
    return response.statusCode < 400;
  }
});
const loginDelayMiddleware = async (req, res, next) => {
  await new Promise(resolve => setTimeout(resolve, 1000));
  next();
};
app.get('/login', async (req, res) => {
  if (req.session.userId) {
    return res.redirect('/dashboard');
  }
  try {
    const usersExist = await checkIfUsersExist();
    if (!usersExist) {
      return res.redirect('/setup-account');
    }
    res.render('login', {
      title: 'Login',
      error: null
    });
  } catch (error) {
    console.error('Error checking for users:', error);
    res.render('login', {
      title: 'Login',
      error: 'System error. Please try again.'
    });
  }
});

// Regular user registration
app.get('/register', async (req, res) => {
  if (req.session.userId) {
    return res.redirect('/dashboard');
  }
  try {
    const usersExist = await checkIfUsersExist();
    if (!usersExist) {
      return res.redirect('/setup-account');
    }
    res.render('register', {
      title: 'Register',
      error: null
    });
  } catch (error) {
    console.error('Error on register page:', error);
    res.render('register', {
      title: 'Register',
      error: 'System error. Please try again.'
    });
  }
});
app.post('/login', loginDelayMiddleware, loginLimiter, async (req, res) => {
  const { username, password } = req.body;
  try {
    const user = await User.findByUsername(username);
    if (!user) {
      return res.render('login', {
        title: 'Login',
        error: 'Invalid username or password'
      });
    }

    // Check if user account is active
    if (!user.is_active) {
      return res.render('login', {
        title: 'Login',
        error: 'Your account has been suspended. Please contact support.'
      });
    }

    const passwordMatch = await User.verifyPassword(password, user.password);
    if (!passwordMatch) {
      return res.render('login', {
        title: 'Login',
        error: 'Invalid username or password'
      });
    }
    req.session.userId = user.id;
    req.session.username = user.username;
    req.session.userRole = user.role || 'user';
    res.redirect('/dashboard');
  } catch (error) {
    console.error('Login error:', error);
    res.render('login', {
      title: 'Login',
      error: 'An error occurred during login. Please try again.'
    });
  }
});

// Regular user registration POST
app.post('/register', upload.single('avatar'), [
  body('username')
    .trim()
    .isLength({ min: 3, max: 20 })
    .withMessage('Username must be between 3 and 20 characters')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('Username can only contain letters, numbers, and underscores'),
  body('email')
    .optional()
    .isEmail()
    .withMessage('Please enter a valid email address'),
  body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/[a-z]/).withMessage('Password must contain at least one lowercase letter')
    .matches(/[A-Z]/).withMessage('Password must contain at least one uppercase letter')
    .matches(/[0-9]/).withMessage('Password must contain at least one number'),
  body('confirmPassword')
    .custom((value, { req }) => value === req.body.password)
    .withMessage('Passwords do not match')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.render('register', {
        title: 'Register',
        user: { username: req.body.username || '', email: req.body.email || '' },
        error: errors.array()[0].msg
      });
    }

    // Check if username already exists
    const existingUsername = await User.findByUsername(req.body.username);
    if (existingUsername) {
      return res.render('register', {
        title: 'Register',
        user: { email: req.body.email || '' },
        error: 'Username is already taken'
      });
    }

    // Check if email already exists (if provided)
    if (req.body.email) {
      const existingEmail = await User.findByEmail(req.body.email);
      if (existingEmail) {
        return res.render('register', {
          title: 'Register',
          user: { username: req.body.username || '' },
          error: 'Email is already registered'
        });
      }
    }

    const avatarPath = req.file ? `/uploads/avatars/${req.file.filename}` : null;

    // Get Preview plan details
    const Subscription = require('./models/Subscription');
    const previewPlan = await new Promise((resolve, reject) => {
      db.get('SELECT * FROM subscription_plans WHERE name = ? AND is_active = 1', ['Preview'], (err, row) => {
        if (err) reject(err);
        else resolve(row);
      });
    });

    if (!previewPlan) {
      return res.render('register', {
        title: 'Register',
        user: { username: req.body.username || '', email: req.body.email || '' },
        error: 'Preview plan not found. Please contact support.'
      });
    }

    console.log('Preview plan found:', previewPlan);

    // Create new user with correct Preview plan limits
    const userId = uuidv4();
    await User.create({
      id: userId,
      username: req.body.username,
      email: req.body.email,
      password: req.body.password,
      avatar_path: avatarPath,
      role: 'user',
      plan_type: 'Preview',
      max_streaming_slots: previewPlan.max_streaming_slots,
      max_storage_gb: previewPlan.max_storage_gb
    });

    // Create automatic subscription for Preview plan
    const endDate = new Date();
    endDate.setFullYear(endDate.getFullYear() + 10); // Preview plan never expires

    await Subscription.createSubscription({
      user_id: userId,
      plan_id: previewPlan.id,
      status: 'active',
      end_date: endDate.toISOString(),
      payment_method: 'free'
    });

    // Set session
    req.session.userId = userId;
    req.session.username = req.body.username;
    req.session.userRole = 'user';
    if (avatarPath) {
      req.session.avatar_path = avatarPath;
    }

    res.redirect('/dashboard');
  } catch (error) {
    console.error('Registration error:', error);
    res.render('register', {
      title: 'Register',
      user: { username: req.body.username || '', email: req.body.email || '' },
      error: 'An error occurred during registration. Please try again.'
    });
  }
});
app.get('/logout', (req, res) => {
  req.session.destroy();
  res.redirect('/login');
});
app.get('/setup-account', async (req, res) => {
  try {
    const usersExist = await checkIfUsersExist();
    if (usersExist && !req.session.userId) {
      return res.redirect('/login');
    }
    if (req.session.userId) {
      const user = await User.findById(req.session.userId);
      if (user && user.username) {
        return res.redirect('/dashboard');
      }
    }
    res.render('setup-account', {
      title: 'Complete Your Account',
      user: req.session.userId ? await User.findById(req.session.userId) : {},
      error: null
    });
  } catch (error) {
    console.error('Setup account error:', error);
    res.redirect('/login');
  }
});
app.post('/setup-account', upload.single('avatar'), [
  body('username')
    .trim()
    .isLength({ min: 3, max: 20 })
    .withMessage('Username must be between 3 and 20 characters')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('Username can only contain letters, numbers, and underscores'),
  body('email')
    .optional()
    .isEmail()
    .withMessage('Please enter a valid email address'),
  body('password')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/[a-z]/).withMessage('Password must contain at least one lowercase letter')
    .matches(/[A-Z]/).withMessage('Password must contain at least one uppercase letter')
    .matches(/[0-9]/).withMessage('Password must contain at least one number'),
  body('confirmPassword')
    .custom((value, { req }) => value === req.body.password)
    .withMessage('Passwords do not match')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      console.log('Validation errors:', errors.array());
      return res.render('setup-account', {
        title: 'Complete Your Account',
        user: { username: req.body.username || '' },
        error: errors.array()[0].msg
      });
    }
    const existingUsername = await User.findByUsername(req.body.username);
    if (existingUsername) {
      return res.render('setup-account', {
        title: 'Complete Your Account',
        user: { email: req.body.email || '' },
        error: 'Username is already taken'
      });
    }

    // Check if email is already taken (if provided)
    if (req.body.email) {
      const existingEmail = await User.findByEmail(req.body.email);
      if (existingEmail) {
        return res.render('setup-account', {
          title: 'Complete Your Account',
          user: { username: req.body.username || '' },
          error: 'Email is already taken'
        });
      }
    }
    const avatarPath = req.file ? `/uploads/avatars/${req.file.filename}` : null;
    const usersExist = await checkIfUsersExist();
    if (!usersExist) {
      try {
        const userId = uuidv4();
        await User.create({
          id: userId,
          username: req.body.username,
          email: req.body.email,
          password: req.body.password,
          avatar_path: avatarPath,
          role: 'admin', // First user is admin
          max_streaming_slots: -1, // Unlimited for admin
          max_storage_gb: 1000 // 1TB for admin
        });
        req.session.userId = userId;
        req.session.username = req.body.username;
        req.session.userRole = 'admin';
        if (avatarPath) {
          req.session.avatar_path = avatarPath;
        }
        return res.redirect('/dashboard');
      } catch (error) {
        console.error('User creation error:', error);
        return res.render('setup-account', {
          title: 'Complete Your Account',
          user: {},
          error: 'Failed to create user. Please try again.'
        });
      }
    } else {
      await User.update(req.session.userId, {
        username: req.body.username,
        password: req.body.password,
        avatar_path: avatarPath,
      });
      req.session.username = req.body.username;
      if (avatarPath) {
        req.session.avatar_path = avatarPath;
      }
      res.redirect('/dashboard');
    }
  } catch (error) {
    console.error('Account setup error:', error);
    res.render('setup-account', {
      title: 'Complete Your Account',
      user: { email: req.body.email || '' },
      error: 'An error occurred. Please try again.'
    });
  }
});
// Mount routes
app.use('/admin', adminRoutes);
app.use('/subscription', subscriptionRoutes);

// Global error handler middleware
app.use((error, req, res, next) => {
  console.error('Global error handler:', error);

  // Track error in performance monitor
  if (performanceMonitor && performanceMonitor.trackError) {
    performanceMonitor.trackError(error);
  }

  // Don't send error details in production
  const isDevelopment = process.env.NODE_ENV !== 'production';

  if (req.xhr || req.headers.accept?.indexOf('json') > -1) {
    // API request - send JSON error
    res.status(500).json({
      error: 'Internal server error',
      message: isDevelopment ? error.message : 'Something went wrong'
    });
  } else {
    // Web request - render error page
    res.status(500).render('error', {
      title: 'Error',
      message: 'Something went wrong',
      error: isDevelopment ? error : {}
    });
  }
});

app.get('/', (req, res) => {
  res.redirect('/dashboard');
});
app.get('/dashboard', isAuthenticated, async (req, res) => {
  try {
    const user = await User.findById(req.session.userId);
    const quotaInfo = await QuotaMiddleware.getUserQuotaInfo(req.session.userId);
    const userStats = await User.getUserStats(req.session.userId);

    res.render('dashboard', {
      title: 'Dashboard',
      active: 'dashboard',
      user: user,
      quota: quotaInfo,
      stats: userStats
    });
  } catch (error) {
    console.error('Dashboard error:', error);
    res.redirect('/login');
  }
});
app.get('/gallery', isAuthenticated, async (req, res) => {
  try {
    const videos = await Video.findAll(req.session.userId);
    res.render('gallery', {
      title: 'Video Gallery',
      active: 'gallery',
      user: await User.findById(req.session.userId),
      videos: videos
    });
  } catch (error) {
    console.error('Gallery error:', error);
    res.redirect('/dashboard');
  }
});
app.get('/settings', isAuthenticated, async (req, res) => {
  try {
    const user = await User.findById(req.session.userId);
    if (!user) {
      req.session.destroy();
      return res.redirect('/login');
    }
    res.render('settings', {
      title: 'Settings',
      active: 'settings',
      user: user
    });
  } catch (error) {
    console.error('Settings error:', error);
    res.redirect('/login');
  }
});
app.get('/history', isAuthenticated, async (req, res) => {
  try {
    const db = require('./db/database').db;
    const history = await new Promise((resolve, reject) => {
      db.all(
        `SELECT h.*, v.thumbnail_path
         FROM stream_history h
         LEFT JOIN videos v ON h.video_id = v.id
         WHERE h.user_id = ?
         ORDER BY h.start_time DESC`,
        [req.session.userId],
        (err, rows) => {
          if (err) reject(err);
          else resolve(rows);
        }
      );
    });
    res.render('history', {
      active: 'history',
      title: 'Stream History',
      history: history,
      helpers: app.locals.helpers
    });
  } catch (error) {
    console.error('Error fetching stream history:', error);
    res.status(500).render('error', {
      title: 'Error',
      message: 'Failed to load stream history',
      error: error
    });
  }
});
app.delete('/api/history/:id', isAuthenticated, async (req, res) => {
  try {
    const db = require('./db/database').db;
    const historyId = req.params.id;
    const history = await new Promise((resolve, reject) => {
      db.get(
        'SELECT * FROM stream_history WHERE id = ? AND user_id = ?',
        [historyId, req.session.userId],
        (err, row) => {
          if (err) reject(err);
          else resolve(row);
        }
      );
    });
    if (!history) {
      return res.status(404).json({
        success: false,
        error: 'History entry not found or not authorized'
      });
    }
    await new Promise((resolve, reject) => {
      db.run(
        'DELETE FROM stream_history WHERE id = ?',
        [historyId],
        function (err) {
          if (err) reject(err);
          else resolve(this);
        }
      );
    });
    res.json({ success: true, message: 'History entry deleted' });
  } catch (error) {
    console.error('Error deleting history entry:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to delete history entry'
    });
  }
});
app.get('/api/system-stats', isAuthenticated, async (req, res) => {
  try {
    const stats = await systemMonitor.getSystemStats();
    res.json(stats);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Performance monitoring API endpoints
app.get('/api/performance/summary', isAuthenticated, (req, res) => {
  try {
    const summary = performanceMonitor.getPerformanceSummary();
    res.json(summary);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/performance/detailed', isAuthenticated, (req, res) => {
  try {
    const report = performanceMonitor.getDetailedReport();
    res.json(report);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/cache/stats', isAuthenticated, (req, res) => {
  try {
    const stats = cacheService.getStats();
    res.json(stats);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/cache/clear', isAuthenticated, (req, res) => {
  try {
    cacheService.clear();
    res.json({ success: true, message: 'Cache cleared successfully' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/database/stats', isAuthenticated, async (req, res) => {
  try {
    const stats = await dbOptimizer.getDatabaseStats();
    res.json(stats);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Load balancing API endpoints
app.get('/api/load-balancer/status', isAuthenticated, (req, res) => {
  try {
    const loadBalancer = require('./services/loadBalancer');
    const status = loadBalancer.getStatus();
    res.json(status);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.get('/api/load-balancer/metrics', isAuthenticated, (req, res) => {
  try {
    const loadBalancer = require('./services/loadBalancer');
    const metrics = loadBalancer.getMetrics();
    res.json(metrics);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/load-balancer/config', isAuthenticated, async (req, res) => {
  try {
    const loadBalancer = require('./services/loadBalancer');
    const result = loadBalancer.updateConfig(req.body);
    res.json(result);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/load-balancer/quality', isAuthenticated, async (req, res) => {
  try {
    const { qualityLevel } = req.body;
    if (!qualityLevel) {
      return res.status(400).json({ error: 'Quality level is required' });
    }

    const loadBalancer = require('./services/loadBalancer');
    const result = await loadBalancer.setQualityLevel(qualityLevel);
    res.json(result);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/load-balancer/start', isAuthenticated, (req, res) => {
  try {
    const loadBalancer = require('./services/loadBalancer');
    loadBalancer.start();
    res.json({ success: true, message: 'Load balancer started' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

app.post('/api/load-balancer/stop', isAuthenticated, (req, res) => {
  try {
    const loadBalancer = require('./services/loadBalancer');
    loadBalancer.stop();
    res.json({ success: true, message: 'Load balancer stopped' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});
function getLocalIpAddresses() {
  const interfaces = os.networkInterfaces();
  const addresses = [];
  Object.keys(interfaces).forEach((ifname) => {
    interfaces[ifname].forEach((iface) => {
      if (iface.family === 'IPv4' && !iface.internal) {
        addresses.push(iface.address);
      }
    });
  });
  return addresses.length > 0 ? addresses : ['localhost'];
}
app.post('/settings/profile', isAuthenticated, upload.single('avatar'), [
  body('username')
    .trim()
    .isLength({ min: 3, max: 20 })
    .withMessage('Username must be between 3 and 20 characters')
    .matches(/^[a-zA-Z0-9_]+$/)
    .withMessage('Username can only contain letters, numbers, and underscores'),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.render('settings', {
        title: 'Settings',
        active: 'settings',
        user: await User.findById(req.session.userId),
        error: errors.array()[0].msg,
        activeTab: 'profile'
      });
    }
    const currentUser = await User.findById(req.session.userId);
    if (req.body.username !== currentUser.username) {
      const existingUser = await User.findByUsername(req.body.username);
      if (existingUser) {
        return res.render('settings', {
          title: 'Settings',
          active: 'settings',
          user: currentUser,
          error: 'Username is already taken',
          activeTab: 'profile'
        });
      }
    }
    const updateData = {
      username: req.body.username
    };
    if (req.file) {
      updateData.avatar_path = `/uploads/avatars/${req.file.filename}`;
    }
    await User.update(req.session.userId, updateData);
    req.session.username = updateData.username;
    if (updateData.avatar_path) {
      req.session.avatar_path = updateData.avatar_path;
    }
    return res.render('settings', {
      title: 'Settings',
      active: 'settings',
      user: await User.findById(req.session.userId),
      success: 'Profile updated successfully!',
      activeTab: 'profile'
    });
  } catch (error) {
    console.error('Error updating profile:', error);
    res.render('settings', {
      title: 'Settings',
      active: 'settings',
      user: await User.findById(req.session.userId),
      error: 'An error occurred while updating your profile',
      activeTab: 'profile'
    });
  }
});
app.post('/settings/password', isAuthenticated, [
  body('currentPassword').notEmpty().withMessage('Current password is required'),
  body('newPassword')
    .isLength({ min: 8 })
    .withMessage('Password must be at least 8 characters long')
    .matches(/[a-z]/).withMessage('Password must contain at least one lowercase letter')
    .matches(/[A-Z]/).withMessage('Password must contain at least one uppercase letter')
    .matches(/[0-9]/).withMessage('Password must contain at least one number'),
  body('confirmPassword')
    .custom((value, { req }) => value === req.body.newPassword)
    .withMessage('Passwords do not match'),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.render('settings', {
        title: 'Settings',
        active: 'settings',
        user: await User.findById(req.session.userId),
        error: errors.array()[0].msg,
        activeTab: 'security'
      });
    }
    const user = await User.findById(req.session.userId);
    const passwordMatch = await User.verifyPassword(req.body.currentPassword, user.password);
    if (!passwordMatch) {
      return res.render('settings', {
        title: 'Settings',
        active: 'settings',
        user: user,
        error: 'Current password is incorrect',
        activeTab: 'security'
      });
    }
    const hashedPassword = await bcrypt.hash(req.body.newPassword, 10);
    await User.update(req.session.userId, { password: hashedPassword });
    return res.render('settings', {
      title: 'Settings',
      active: 'settings',
      user: await User.findById(req.session.userId),
      success: 'Password changed successfully',
      activeTab: 'security'
    });
  } catch (error) {
    console.error('Error changing password:', error);
    res.render('settings', {
      title: 'Settings',
      active: 'settings',
      user: await User.findById(req.session.userId),
      error: 'An error occurred while changing your password',
      activeTab: 'security'
    });
  }
});
app.get('/settings', isAuthenticated, async (req, res) => {
  try {
    const user = await User.findById(req.session.userId);
    if (!user) {
      req.session.destroy();
      return res.redirect('/login');
    }
    res.render('settings', {
      title: 'Settings',
      active: 'settings',
      user: user
    });
  } catch (error) {
    console.error('Settings error:', error);
    res.redirect('/dashboard');
  }
});
app.post('/settings/integrations/gdrive', isAuthenticated, [
  body('apiKey').notEmpty().withMessage('API Key is required'),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.render('settings', {
        title: 'Settings',
        active: 'settings',
        user: await User.findById(req.session.userId),
        error: errors.array()[0].msg,
        activeTab: 'integrations'
      });
    }
    await User.update(req.session.userId, {
      gdrive_api_key: req.body.apiKey
    });
    return res.render('settings', {
      title: 'Settings',
      active: 'settings',
      user: await User.findById(req.session.userId),
      success: 'Google Drive API key saved successfully!',
      activeTab: 'integrations'
    });
  } catch (error) {
    console.error('Error saving Google Drive API key:', error);
    res.render('settings', {
      title: 'Settings',
      active: 'settings',
      user: await User.findById(req.session.userId),
      error: 'An error occurred while saving your Google Drive API key',
      activeTab: 'integrations'
    });
  }
});
app.post('/upload/video', isAuthenticated, uploadVideo.single('video'), QuotaMiddleware.checkStorageQuota(), QuotaMiddleware.updateStorageUsage(), async (req, res) => {
  try {
    console.log('Upload request received:', req.file);

    if (!req.file) {
      return res.status(400).json({ error: 'No video file provided' });
    }
    const { filename, originalname, path: videoPath, mimetype, size } = req.file;
    const thumbnailName = path.basename(filename, path.extname(filename)) + '.jpg';
    const videoInfo = await getVideoInfo(videoPath);
    const thumbnailRelativePath = await generateThumbnail(videoPath, thumbnailName)
      .then(() => `/uploads/thumbnails/${thumbnailName}`)
      .catch(() => null);
    let format = 'unknown';
    if (mimetype === 'video/mp4') format = 'mp4';
    else if (mimetype === 'video/avi') format = 'avi';
    else if (mimetype === 'video/quicktime') format = 'mov';
    const videoData = {
      title: path.basename(originalname, path.extname(originalname)),
      original_filename: originalname,
      filepath: `/uploads/videos/${filename}`,
      thumbnail_path: thumbnailRelativePath,
      file_size: size,
      duration: videoInfo.duration,
      format: format,
      user_id: req.session.userId
    };
    const video = await Video.create(videoData);
    res.json({
      success: true,
      video: {
        id: video.id,
        title: video.title,
        filepath: video.filepath,
        thumbnail_path: video.thumbnail_path,
        duration: video.duration,
        file_size: video.file_size,
        format: video.format
      }
    });
  } catch (error) {
    console.error('Upload error details:', error);
    res.status(500).json({
      error: 'Failed to upload video',
      details: error.message
    });
  }
});
app.post('/api/videos/upload', isAuthenticated, videoUpload.single('video'), QuotaMiddleware.checkStorageQuota(), async (req, res) => {
  try {
    console.log('Upload request received:', req.file);

    if (!req.file) {
      return res.status(400).json({ error: 'No video file provided' });
    }
    let title = path.parse(req.file.originalname).name;
    const filePath = `/uploads/videos/${req.file.filename}`;
    const fullFilePath = path.join(__dirname, 'public', filePath);
    const fileSize = req.file.size;
    await new Promise((resolve, reject) => {
      ffmpeg.ffprobe(fullFilePath, (err, metadata) => {
        if (err) {
          console.error('Error extracting metadata:', err);
          return reject(err);
        }
        const videoStream = metadata.streams.find(stream => stream.codec_type === 'video');
        const audioStream = metadata.streams.find(stream => stream.codec_type === 'audio');
        const duration = metadata.format.duration || 0;
        const format = metadata.format.format_name || '';
        const resolution = videoStream ? `${videoStream.width}x${videoStream.height}` : '';
        const bitrate = metadata.format.bit_rate ?
          Math.round(parseInt(metadata.format.bit_rate) / 1000) :
          null;
        let fps = null;
        if (videoStream && videoStream.avg_frame_rate) {
          const fpsRatio = videoStream.avg_frame_rate.split('/');
          if (fpsRatio.length === 2 && parseInt(fpsRatio[1]) !== 0) {
            fps = Math.round((parseInt(fpsRatio[0]) / parseInt(fpsRatio[1]) * 100)) / 100;
          } else {
            fps = parseInt(fpsRatio[0]) || null;
          }
        }

        // Extract codec information
        const codec = videoStream ? videoStream.codec_name : null;
        const audioCodec = audioStream ? audioStream.codec_name : null;

        console.log(`[Upload] Video codec info: ${codec}, Audio codec: ${audioCodec}`);
        const thumbnailFilename = `thumb-${path.parse(req.file.filename).name}.jpg`;
        const thumbnailPath = `/uploads/thumbnails/${thumbnailFilename}`;
        const fullThumbnailPath = path.join(__dirname, 'public', thumbnailPath);
        ffmpeg(fullFilePath)
          .inputOptions([
            '-hwaccel', 'none',
            '-ss', '00:00:10'
          ])
          .outputOptions([
            '-vframes', '1',
            '-q:v', '2',
            '-vf', 'scale=854:480:force_original_aspect_ratio=decrease,pad=854:480:(ow-iw)/2:(oh-ih)/2'
          ])
          .output(fullThumbnailPath)
          .on('end', async () => {
            try {
              const videoData = {
                title,
                filepath: filePath,
                thumbnail_path: thumbnailPath,
                file_size: fileSize,
                duration,
                format,
                resolution,
                bitrate,
                fps,
                codec,
                audioCodec,
                user_id: req.session.userId
              };
              const video = await Video.create(videoData);

              // Update storage usage after successful upload
              if (req.uploadSizeGB && req.session.userId) {
                console.log(`📊 Updating storage: +${req.uploadSizeGB.toFixed(3)}GB for user ${req.session.userId}`);
                await Subscription.updateStorageUsage(req.session.userId, req.uploadSizeGB);
                console.log('✅ Storage updated successfully');
              }

              res.json({
                success: true,
                message: 'Video uploaded successfully',
                video
              });
              resolve();
            } catch (dbError) {
              console.error('Database error:', dbError);
              reject(dbError);
            }
          })
          .on('error', (err) => {
            console.error('Error creating thumbnail:', err);
            reject(err);
          })
          .run();
      });
    });
  } catch (error) {
    console.error('Upload error details:', error);
    res.status(500).json({
      error: 'Failed to upload video',
      details: error.message
    });
  }
});
app.get('/api/videos', isAuthenticated, async (req, res) => {
  try {
    const videos = await Video.findAll(req.session.userId);
    res.json({ success: true, videos });
  } catch (error) {
    console.error('Error fetching videos:', error);
    res.status(500).json({ success: false, error: 'Failed to fetch videos' });
  }
});
app.delete('/api/videos/:id', isAuthenticated, async (req, res) => {
  try {
    const videoId = req.params.id;
    const video = await Video.findById(videoId);
    if (!video) {
      return res.status(404).json({ success: false, error: 'Video not found' });
    }
    if (video.user_id !== req.session.userId) {
      return res.status(403).json({ success: false, error: 'Not authorized' });
    }

    // Calculate storage to subtract (convert bytes to GB)
    const fileSizeGB = video.file_size ? video.file_size / (1024 * 1024 * 1024) : 0;
    console.log(`🗑️ Deleting video: ${video.title}, Size: ${fileSizeGB.toFixed(3)}GB`);

    const videoPath = path.join(__dirname, 'public', video.filepath);
    if (fs.existsSync(videoPath)) {
      fs.unlinkSync(videoPath);
    }
    if (video.thumbnail_path) {
      const thumbnailPath = path.join(__dirname, 'public', video.thumbnail_path);
      if (fs.existsSync(thumbnailPath)) {
        fs.unlinkSync(thumbnailPath);
      }
    }

    // Delete video from database
    await Video.delete(videoId, req.session.userId);

    // Update storage usage (subtract the file size)
    if (fileSizeGB > 0) {
      console.log(`📊 Updating storage: -${fileSizeGB.toFixed(3)}GB for user ${req.session.userId}`);
      await Subscription.updateStorageUsage(req.session.userId, -fileSizeGB);
      console.log('✅ Storage updated successfully');
    }

    res.json({ success: true, message: 'Video deleted successfully' });
  } catch (error) {
    console.error('Error deleting video:', error);
    res.status(500).json({ success: false, error: 'Failed to delete video' });
  }
});
app.post('/api/videos/:id/rename', isAuthenticated, [
  body('title').trim().isLength({ min: 1 }).withMessage('Title cannot be empty')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ error: errors.array()[0].msg });
    }
    const video = await Video.findById(req.params.id);
    if (!video) {
      return res.status(404).json({ error: 'Video not found' });
    }
    if (video.user_id !== req.session.userId) {
      return res.status(403).json({ error: 'You don\'t have permission to rename this video' });
    }
    await Video.update(req.params.id, { title: req.body.title });
    res.json({ success: true, message: 'Video renamed successfully' });
  } catch (error) {
    console.error('Error renaming video:', error);
    res.status(500).json({ error: 'Failed to rename video' });
  }
});
app.get('/stream/:videoId', isAuthenticated, async (req, res) => {
  try {
    const videoId = req.params.videoId;
    const video = await Video.findById(videoId);
    if (!video) {
      return res.status(404).send('Video not found');
    }
    if (video.user_id !== req.session.userId) {
      return res.status(403).send('You do not have permission to access this video');
    }
    const videoPath = path.join(__dirname, 'public', video.filepath);

    // Check if file exists
    if (!fs.existsSync(videoPath)) {
      console.error(`Video file not found: ${videoPath}`);
      return res.status(404).send('Video file not found on server');
    }

    const stat = fs.statSync(videoPath);
    const fileSize = stat.size;
    const range = req.headers.range;

    // Set appropriate headers for video streaming
    res.setHeader('Content-Disposition', 'inline');
    res.setHeader('X-Content-Type-Options', 'nosniff');
    res.setHeader('Cache-Control', 'public, max-age=3600'); // Cache for 1 hour
    res.setHeader('Accept-Ranges', 'bytes');

    // Determine content type based on file extension
    const ext = path.extname(video.filepath).toLowerCase();
    let contentType = 'video/mp4';
    if (ext === '.avi') contentType = 'video/x-msvideo';
    else if (ext === '.mov') contentType = 'video/quicktime';

    if (range) {
      const parts = range.replace(/bytes=/, '').split('-');
      const start = parseInt(parts[0], 10);
      const end = parts[1] ? parseInt(parts[1], 10) : fileSize - 1;
      const chunkSize = (end - start) + 1;

      if (start >= fileSize || end >= fileSize) {
        return res.status(416).send('Range Not Satisfiable');
      }

      const file = fs.createReadStream(videoPath, { start, end });
      res.writeHead(206, {
        'Content-Range': `bytes ${start}-${end}/${fileSize}`,
        'Content-Length': chunkSize,
        'Content-Type': contentType,
      });

      file.on('error', (err) => {
        console.error('File stream error:', err);
        if (!res.headersSent) {
          res.status(500).send('Error streaming video');
        }
      });

      file.pipe(res);
    } else {
      res.writeHead(200, {
        'Content-Length': fileSize,
        'Content-Type': contentType,
      });

      const stream = fs.createReadStream(videoPath);
      stream.on('error', (err) => {
        console.error('File stream error:', err);
        if (!res.headersSent) {
          res.status(500).send('Error streaming video');
        }
      });

      stream.pipe(res);
    }
  } catch (error) {
    console.error('Streaming error:', error);
    if (!res.headersSent) {
      res.status(500).send('Error streaming video');
    }
  }
});
app.get('/api/settings/gdrive-status', isAuthenticated, async (req, res) => {
  try {
    const user = await User.findById(req.session.userId);
    res.json({
      hasApiKey: !!user.gdrive_api_key,
      message: user.gdrive_api_key ? 'Google Drive API key is configured' : 'No Google Drive API key found'
    });
  } catch (error) {
    console.error('Error checking Google Drive API status:', error);
    res.status(500).json({ error: 'Failed to check API key status' });
  }
});
app.post('/api/settings/gdrive-api-key', isAuthenticated, [
  body('apiKey').notEmpty().withMessage('API Key is required'),
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        success: false,
        error: errors.array()[0].msg
      });
    }
    await User.update(req.session.userId, {
      gdrive_api_key: req.body.apiKey
    });
    return res.json({
      success: true,
      message: 'Google Drive API key saved successfully!'
    });
  } catch (error) {
    console.error('Error saving Google Drive API key:', error);
    res.status(500).json({
      success: false,
      error: 'An error occurred while saving your Google Drive API key'
    });
  }
});
app.post('/api/videos/import-drive', isAuthenticated, [
  body('driveUrl').notEmpty().withMessage('Google Drive URL is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, error: errors.array()[0].msg });
    }
    const { driveUrl } = req.body;
    const user = await User.findById(req.session.userId);
    if (!user.gdrive_api_key) {
      return res.status(400).json({
        success: false,
        error: 'Google Drive API key is not configured'
      });
    }
    const { extractFileId, downloadFile } = require('./utils/googleDriveService');
    try {
      const fileId = extractFileId(driveUrl);
      const jobId = uuidv4();
      processGoogleDriveImport(jobId, user.gdrive_api_key, fileId, req.session.userId)
        .catch(err => console.error('Drive import failed:', err));
      return res.json({
        success: true,
        message: 'Video import started',
        jobId: jobId
      });
    } catch (error) {
      console.error('Google Drive URL parsing error:', error);
      return res.status(400).json({
        success: false,
        error: 'Invalid Google Drive URL format'
      });
    }
  } catch (error) {
    console.error('Error importing from Google Drive:', error);
    res.status(500).json({ success: false, error: 'Failed to import video' });
  }
});
app.get('/api/videos/import-status/:jobId', isAuthenticated, async (req, res) => {
  const jobId = req.params.jobId;
  if (!importJobs[jobId]) {
    return res.status(404).json({ success: false, error: 'Import job not found' });
  }
  return res.json({
    success: true,
    status: importJobs[jobId]
  });
});
const importJobs = {};
async function processGoogleDriveImport(jobId, apiKey, fileId, userId) {
  const { downloadFile } = require('./utils/googleDriveService');
  const { getVideoInfo, generateThumbnail } = require('./utils/videoProcessor');
  const ffmpeg = require('fluent-ffmpeg');

  importJobs[jobId] = {
    status: 'downloading',
    progress: 0,
    message: 'Starting download...'
  };

  try {
    const result = await downloadFile(apiKey, fileId, (progress) => {
      importJobs[jobId] = {
        status: 'downloading',
        progress: progress.progress,
        message: `Downloading ${progress.filename}: ${progress.progress}%`
      };
    });

    importJobs[jobId] = {
      status: 'processing',
      progress: 100,
      message: 'Processing video...'
    };

    const videoInfo = await getVideoInfo(result.localFilePath);

    const metadata = await new Promise((resolve, reject) => {
      ffmpeg.ffprobe(result.localFilePath, (err, metadata) => {
        if (err) return reject(err);
        resolve(metadata);
      });
    });

    let resolution = '';
    let bitrate = null;

    const videoStream = metadata.streams.find(stream => stream.codec_type === 'video');
    if (videoStream) {
      resolution = `${videoStream.width}x${videoStream.height}`;
    }

    if (metadata.format && metadata.format.bit_rate) {
      bitrate = Math.round(parseInt(metadata.format.bit_rate) / 1000);
    }

    const thumbnailName = path.basename(result.filename, path.extname(result.filename)) + '.jpg';
    const thumbnailRelativePath = await generateThumbnail(result.localFilePath, thumbnailName)
      .then(() => `/uploads/thumbnails/${thumbnailName}`)
      .catch(() => null);

    let format = path.extname(result.filename).toLowerCase().replace('.', '');
    if (!format) format = 'mp4';

    const videoData = {
      title: path.basename(result.originalFilename, path.extname(result.originalFilename)),
      filepath: `/uploads/videos/${result.filename}`,
      thumbnail_path: thumbnailRelativePath,
      file_size: result.fileSize,
      duration: videoInfo.duration,
      format: format,
      resolution: resolution,
      bitrate: bitrate,
      user_id: userId
    };

    const video = await Video.create(videoData);

    // Update storage usage for Google Drive import
    const fileSizeGB = result.fileSize / (1024 * 1024 * 1024);
    console.log(`📊 Updating storage for Google Drive import: +${fileSizeGB.toFixed(3)}GB for user ${userId}`);
    await Subscription.updateStorageUsage(userId, fileSizeGB);
    console.log('✅ Storage updated successfully for Google Drive import');

    importJobs[jobId] = {
      status: 'complete',
      progress: 100,
      message: 'Video imported successfully',
      videoId: video.id
    };
    setTimeout(() => {
      delete importJobs[jobId];
    }, 5 * 60 * 1000);
  } catch (error) {
    console.error('Error processing Google Drive import:', error);
    importJobs[jobId] = {
      status: 'failed',
      progress: 0,
      message: error.message || 'Failed to import video'
    };
    setTimeout(() => {
      delete importJobs[jobId];
    }, 5 * 60 * 1000);
  }
}
app.get('/api/stream/videos', isAuthenticated, async (req, res) => {
  try {
    const videos = await Video.findAll(req.session.userId);
    const formattedVideos = videos.map(video => {
      const duration = video.duration ? Math.floor(video.duration) : 0;
      const minutes = Math.floor(duration / 60);
      const seconds = Math.floor(duration % 60);
      const formattedDuration = `${minutes}:${seconds.toString().padStart(2, '0')}`;
      return {
        id: video.id,
        name: video.title,
        thumbnail: video.thumbnail_path,
        resolution: video.resolution || '1280x720',
        duration: formattedDuration,
        url: `/stream/${video.id}`
      };
    });
    res.json(formattedVideos);
  } catch (error) {
    console.error('Error fetching videos for stream:', error);
    res.status(500).json({ error: 'Failed to load videos' });
  }
});
const Stream = require('./models/Stream');
const { title } = require('process');
app.get('/api/streams', isAuthenticated, async (req, res) => {
  try {
    const filter = req.query.filter;
    const streams = await Stream.findAll(req.session.userId, filter);
    res.json({ success: true, streams });
  } catch (error) {
    console.error('Error fetching streams:', error);
    res.status(500).json({ success: false, error: 'Failed to fetch streams' });
  }
});
app.post('/api/streams', isAuthenticated, QuotaMiddleware.checkValidSubscription(), QuotaMiddleware.checkStreamingQuota(), [
  body('streamTitle').trim().isLength({ min: 1 }).withMessage('Title is required'),
  body('rtmpUrl').trim().isLength({ min: 1 }).withMessage('RTMP URL is required'),
  body('streamKey').trim().isLength({ min: 1 }).withMessage('Stream key is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, error: errors.array()[0].msg });
    }

    // Enhanced RTMP and Stream Key validation
    const StreamKeyValidator = require('./utils/streamKeyValidator');
    const validation = StreamKeyValidator.validateRtmpConfig(req.body.rtmpUrl, req.body.streamKey);

    if (!validation.isValid) {
      return res.status(400).json({
        success: false,
        error: 'Invalid RTMP configuration',
        details: {
          errors: validation.errors,
          warnings: validation.warnings,
          suggestions: validation.suggestions,
          platform: validation.platform
        }
      });
    }
    const isInUse = await Stream.isStreamKeyInUse(req.body.streamKey, req.session.userId);
    if (isInUse) {
      return res.status(400).json({
        success: false,
        error: 'This stream key is already in use. Please use a different key.'
      });
    }
    let platform = 'Custom';
    let platform_icon = 'ti-broadcast';
    if (req.body.rtmpUrl.includes('youtube.com')) {
      platform = 'YouTube';
      platform_icon = 'ti-brand-youtube';
    } else if (req.body.rtmpUrl.includes('facebook.com')) {
      platform = 'Facebook';
      platform_icon = 'ti-brand-facebook';
    } else if (req.body.rtmpUrl.includes('twitch.tv')) {
      platform = 'Twitch';
      platform_icon = 'ti-brand-twitch';
    } else if (req.body.rtmpUrl.includes('tiktok.com')) {
      platform = 'TikTok';
      platform_icon = 'ti-brand-tiktok';
    } else if (req.body.rtmpUrl.includes('instagram.com')) {
      platform = 'Instagram';
      platform_icon = 'ti-brand-instagram';
    } else if (req.body.rtmpUrl.includes('shopee.io')) {
      platform = 'Shopee Live';
      platform_icon = 'ti-shopping-bag';
    } else if (req.body.rtmpUrl.includes('restream.io')) {
      platform = 'Restream.io';
      platform_icon = 'ti-live-photo';
    }
    const streamData = {
      title: req.body.streamTitle,
      video_id: req.body.videoId || null,
      rtmp_url: req.body.rtmpUrl,
      stream_key: req.body.streamKey,
      platform,
      platform_icon,
      bitrate: parseInt(req.body.bitrate) || 2500,
      resolution: req.body.resolution || '1280x720',
      fps: parseInt(req.body.fps) || 30,
      orientation: req.body.orientation || 'horizontal',
      loop_video: req.body.loopVideo === 'true' || req.body.loopVideo === true,
      use_advanced_settings: req.body.useAdvancedSettings === 'true' || req.body.useAdvancedSettings === true,
      user_id: req.session.userId
    };
    if (req.body.scheduleTime) {
      streamData.schedule_time = new Date(req.body.scheduleTime).toISOString();
    }
    if (req.body.duration) {
      streamData.duration = parseInt(req.body.duration);
    }
    streamData.status = req.body.scheduleTime ? 'scheduled' : 'offline';
    const stream = await Stream.create(streamData);
    res.json({ success: true, stream });
  } catch (error) {
    console.error('Error creating stream:', error);
    res.status(500).json({ success: false, error: 'Failed to create stream' });
  }
});
app.get('/api/streams/:id', isAuthenticated, async (req, res) => {
  try {
    const stream = await Stream.getStreamWithVideo(req.params.id);
    if (!stream) {
      return res.status(404).json({ success: false, error: 'Stream not found' });
    }
    if (stream.user_id !== req.session.userId) {
      return res.status(403).json({ success: false, error: 'Not authorized to access this stream' });
    }
    res.json({ success: true, stream });
  } catch (error) {
    console.error('Error fetching stream:', error);
    res.status(500).json({ success: false, error: 'Failed to fetch stream' });
  }
});
app.put('/api/streams/:id', isAuthenticated, async (req, res) => {
  try {
    const stream = await Stream.findById(req.params.id);
    if (!stream) {
      return res.status(404).json({ success: false, error: 'Stream not found' });
    }
    if (stream.user_id !== req.session.userId) {
      return res.status(403).json({ success: false, error: 'Not authorized to update this stream' });
    }
    const updateData = {};
    if (req.body.streamTitle) updateData.title = req.body.streamTitle;
    if (req.body.videoId) updateData.video_id = req.body.videoId;
    if (req.body.rtmpUrl) {
      updateData.rtmp_url = req.body.rtmpUrl;

      // Update platform and platform_icon based on new RTMP URL
      let platform = 'Custom';
      let platform_icon = 'ti-broadcast';
      if (req.body.rtmpUrl.includes('youtube.com')) {
        platform = 'YouTube';
        platform_icon = 'ti-brand-youtube';
      } else if (req.body.rtmpUrl.includes('facebook.com')) {
        platform = 'Facebook';
        platform_icon = 'ti-brand-facebook';
      } else if (req.body.rtmpUrl.includes('twitch.tv')) {
        platform = 'Twitch';
        platform_icon = 'ti-brand-twitch';
      } else if (req.body.rtmpUrl.includes('tiktok.com')) {
        platform = 'TikTok';
        platform_icon = 'ti-brand-tiktok';
      } else if (req.body.rtmpUrl.includes('instagram.com')) {
        platform = 'Instagram';
        platform_icon = 'ti-brand-instagram';
      } else if (req.body.rtmpUrl.includes('shopee.io')) {
        platform = 'Shopee Live';
        platform_icon = 'ti-shopping-bag';
      } else if (req.body.rtmpUrl.includes('restream.io')) {
        platform = 'Restream.io';
        platform_icon = 'ti-live-photo';
      }
      updateData.platform = platform;
      updateData.platform_icon = platform_icon;
    }
    if (req.body.streamKey) updateData.stream_key = req.body.streamKey;
    if (req.body.bitrate) updateData.bitrate = parseInt(req.body.bitrate);
    if (req.body.resolution) updateData.resolution = req.body.resolution;
    if (req.body.fps) updateData.fps = parseInt(req.body.fps);
    if (req.body.orientation) updateData.orientation = req.body.orientation;
    if (req.body.loopVideo !== undefined) {
      updateData.loop_video = req.body.loopVideo === 'true' || req.body.loopVideo === true;
    }
    if (req.body.useAdvancedSettings !== undefined) {
      updateData.use_advanced_settings = req.body.useAdvancedSettings === 'true' || req.body.useAdvancedSettings === true;
    }
    if (req.body.scheduleTime) {
      updateData.schedule_time = new Date(req.body.scheduleTime).toISOString();
    }
    if (req.body.duration) {
      updateData.duration = parseInt(req.body.duration);
    }
    if (req.body.scheduleTime) {
      updateData.schedule_time = new Date(req.body.scheduleTime).toISOString();
      updateData.status = 'scheduled';
    } else if ('scheduleTime' in req.body && !req.body.scheduleTime) {
      updateData.schedule_time = null;
      updateData.status = 'offline';
    }
    const updatedStream = await Stream.update(req.params.id, updateData);

    // Clear failed stream status when RTMP settings are updated
    // This allows users to retry with new settings without manual clear
    let failedStatusCleared = false;
    if (updateData.rtmp_url || updateData.stream_key) {
      const wasBlacklisted = await streamingService.clearFailedStream(req.params.id);
      if (wasBlacklisted) {
        console.log(`[API] Automatically cleared failed status for stream ${req.params.id} after RTMP update`);
        failedStatusCleared = true;
      }
    }

    res.json({
      success: true,
      stream: updatedStream,
      failedStatusCleared: failedStatusCleared
    });
  } catch (error) {
    console.error('Error updating stream:', error);
    res.status(500).json({ success: false, error: 'Failed to update stream' });
  }
});
app.delete('/api/streams/:id', isAuthenticated, async (req, res) => {
  try {
    const stream = await Stream.findById(req.params.id);
    if (!stream) {
      return res.status(404).json({ success: false, error: 'Stream not found' });
    }
    if (stream.user_id !== req.session.userId) {
      return res.status(403).json({ success: false, error: 'Not authorized to delete this stream' });
    }
    await Stream.delete(req.params.id, req.session.userId);
    res.json({ success: true, message: 'Stream deleted successfully' });
  } catch (error) {
    console.error('Error deleting stream:', error);
    res.status(500).json({ success: false, error: 'Failed to delete stream' });
  }
});
app.post('/api/streams/:id/status', isAuthenticated, [
  body('status').isIn(['live', 'offline', 'scheduled']).withMessage('Invalid status')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, error: errors.array()[0].msg });
    }
    const streamId = req.params.id;
    const stream = await Stream.findById(streamId);
    if (!stream) {
      return res.status(404).json({ success: false, error: 'Stream not found' });
    }
    if (stream.user_id !== req.session.userId) {
      return res.status(403).json({ success: false, error: 'Not authorized' });
    }
    const newStatus = req.body.status;
    if (newStatus === 'live') {
      if (stream.status === 'live') {
        return res.json({
          success: false,
          error: 'Stream is already live',
          stream
        });
      }
      if (!stream.video_id) {
        return res.json({
          success: false,
          error: 'No video attached to this stream',
          stream
        });
      }
      const result = await streamingService.startStream(streamId);
      if (result.success) {
        const updatedStream = await Stream.getStreamWithVideo(streamId);
        return res.json({
          success: true,
          stream: updatedStream,
          isAdvancedMode: result.isAdvancedMode
        });
      } else {
        return res.status(500).json({
          success: false,
          error: result.error || 'Failed to start stream'
        });
      }
    } else if (newStatus === 'offline') {
      if (stream.status === 'live') {
        const result = await streamingService.stopStream(streamId);
        if (!result.success) {
          console.warn('Failed to stop FFmpeg process:', result.error);
        }
        await Stream.update(streamId, {
          schedule_time: null
        });
        console.log(`Reset schedule_time for stopped stream ${streamId}`);
      } else if (stream.status === 'scheduled') {
        await Stream.update(streamId, {
          schedule_time: null,
          status: 'offline'
        });
        console.log(`Scheduled stream ${streamId} was cancelled`);
      }
      const result = await Stream.updateStatus(streamId, 'offline', req.session.userId);
      if (!result.updated) {
        return res.status(404).json({
          success: false,
          error: 'Stream not found or not updated'
        });
      }
      return res.json({ success: true, stream: result });
    } else {
      const result = await Stream.updateStatus(streamId, newStatus, req.session.userId);
      if (!result.updated) {
        return res.status(404).json({
          success: false,
          error: 'Stream not found or not updated'
        });
      }
      return res.json({ success: true, stream: result });
    }
  } catch (error) {
    console.error('Error updating stream status:', error);
    res.status(500).json({ success: false, error: 'Failed to update stream status' });
  }
});
app.get('/api/streams/check-key', isAuthenticated, async (req, res) => {
  try {
    const streamKey = req.query.key;
    const excludeId = req.query.excludeId || null;
    if (!streamKey) {
      return res.status(400).json({
        success: false,
        error: 'Stream key is required'
      });
    }
    const isInUse = await Stream.isStreamKeyInUse(streamKey, req.session.userId, excludeId);
    res.json({
      success: true,
      isInUse: isInUse,
      message: isInUse ? 'Stream key is already in use' : 'Stream key is available'
    });
  } catch (error) {
    console.error('Error checking stream key:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to check stream key'
    });
  }
});
app.get('/api/streams/:id/logs', isAuthenticated, async (req, res) => {
  try {
    const streamId = req.params.id;
    const stream = await Stream.findById(streamId);
    if (!stream) {
      return res.status(404).json({ success: false, error: 'Stream not found' });
    }
    if (stream.user_id !== req.session.userId) {
      return res.status(403).json({ success: false, error: 'Not authorized' });
    }
    const logs = streamingService.getStreamLogs(streamId);
    const isActive = streamingService.isStreamActive(streamId);
    res.json({
      success: true,
      logs,
      isActive,
      stream
    });
  } catch (error) {
    console.error('Error fetching stream logs:', error);
    res.status(500).json({ success: false, error: 'Failed to fetch stream logs' });
  }
});

// API endpoint untuk validasi RTMP configuration real-time
app.post('/api/validate-rtmp', isAuthenticated, [
  body('rtmpUrl').trim().isLength({ min: 1 }).withMessage('RTMP URL is required'),
  body('streamKey').trim().isLength({ min: 1 }).withMessage('Stream key is required')
], async (req, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({ success: false, error: errors.array()[0].msg });
    }

    const StreamKeyValidator = require('./utils/streamKeyValidator');
    const validation = StreamKeyValidator.validateRtmpConfig(req.body.rtmpUrl, req.body.streamKey);

    res.json({
      success: true,
      validation: {
        isValid: validation.isValid,
        errors: validation.errors,
        warnings: validation.warnings,
        suggestions: validation.suggestions,
        platform: validation.platform,
        platformHelp: StreamKeyValidator.getPlatformHelp(validation.platform)
      }
    });
  } catch (error) {
    console.error('Error validating RTMP config:', error);
    res.status(500).json({ success: false, error: 'Failed to validate RTMP configuration' });
  }
});

// API endpoint untuk clear failed stream status
app.post('/api/streams/:id/clear-failed', isAuthenticated, async (req, res) => {
  try {
    const streamId = req.params.id;
    const stream = await Stream.findById(streamId);

    if (!stream) {
      return res.status(404).json({ success: false, error: 'Stream not found' });
    }

    if (stream.user_id !== req.session.userId) {
      return res.status(403).json({ success: false, error: 'Not authorized' });
    }

    const wasBlacklisted = await streamingService.clearFailedStream(streamId);

    res.json({
      success: true,
      message: wasBlacklisted ? 'Stream failure status cleared and status reset to offline' : 'Stream was not in failed state',
      wasBlacklisted,
      statusReset: stream.status === 'error'
    });
  } catch (error) {
    console.error('Error clearing failed stream:', error);
    res.status(500).json({ success: false, error: 'Failed to clear stream status' });
  }
});

// API endpoint untuk mendapatkan status stream real-time
app.get('/api/streams/status', isAuthenticated, async (req, res) => {
  try {
    console.log(`[API] /api/streams/status called for user ${req.session.userId}`); // Debug log
    const streams = await Stream.findAll(req.session.userId);
    console.log(`[API] Found ${streams ? streams.length : 0} streams in database`); // Debug log

    const activeStreamIds = streamingService.getActiveStreams();
    console.log(`[API] Active stream IDs:`, activeStreamIds); // Debug log

    // Update status berdasarkan active streams di memory
    const updatedStreams = streams.map(stream => ({
      ...stream,
      isReallyActive: activeStreamIds.includes(stream.id),
      actualStatus: activeStreamIds.includes(stream.id) ? 'live' :
                   (stream.status === 'live' ? 'inconsistent' : stream.status)
    }));

    console.log(`[API] Returning ${updatedStreams.length} streams with status updates`); // Debug log

    res.json({
      success: true,
      streams: updatedStreams,
      activeCount: activeStreamIds.length,
      totalCount: streams.length
    });
  } catch (error) {
    console.error('Error fetching stream status:', error);
    res.status(500).json({ success: false, error: 'Failed to fetch stream status' });
  }
});
app.get('/api/server-time', (req, res) => {
  const now = new Date();
  const day = String(now.getDate()).padStart(2, '0');
  const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
  const month = monthNames[now.getMonth()];
  const year = now.getFullYear();
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');
  const formattedTime = `${day} ${month} ${year} ${hours}:${minutes}:${seconds}`;
  res.json({
    serverTime: now.toISOString(),
    formattedTime: formattedTime
  });
});
// Initialize database and services before starting server
async function initializeApplication() {
  try {
    console.log('Checking database schema...');

    // Ensure database schema exists before any services start
    await new Promise((resolve, reject) => {
      db.serialize(() => {
        // Check if tables exist, create if they don't
        db.get("SELECT name FROM sqlite_master WHERE type='table' AND name='streams'", (err, row) => {
          if (err) {
            console.error('Error checking database schema:', err);
            return reject(err);
          }

          if (!row) {
            console.log('⚠️  Database tables missing, running migration...');
            // Run database migration
            require('./db/migrate')().then(() => {
              console.log('✅ Database migration completed');
              resolve();
            }).catch(reject);
          } else {
            console.log('✅ Database schema verified');
            resolve();
          }
        });
      });
    });

    console.log('Database migration completed successfully');
  } catch (error) {
    console.error('❌ Error during database initialization:', error);
    throw error;
  }
}

app.listen(port, '0.0.0.0', async () => {
  const ipAddresses = getLocalIpAddresses();
  console.log(`StreamFlow running at:`);
  if (ipAddresses && ipAddresses.length > 0) {
    ipAddresses.forEach(ip => {
      console.log(`  http://${ip}:${port}`);
    });
  } else {
    console.log(`  http://localhost:${port}`);
  }

  try {
    // Initialize database first
    await initializeApplication();

    // Initialize streaming service with proper error handling
    console.log('[StreamingService] Cleaning up orphaned streams...');
    await streamingService.cleanupOrphanedStreams();

    // Initialize scheduler
    schedulerService.init(streamingService);

    // Sync stream statuses
    console.log('[StreamingService] Syncing stream statuses...');
    await streamingService.syncStreamStatuses();

    // Initialize performance optimizations
    console.log('🚀 Initializing performance optimizations...');

    // Initialize database optimizations
    await dbOptimizer.initialize();

    // Preload common data to cache
    await cacheService.preloadCommonData();

    // Precompress static assets
    const publicDir = path.join(__dirname, 'public');
    await staticOptimization.precompressAssets(publicDir);

    console.log('✅ Performance optimizations initialized successfully');

    // Initialize load balancer
    const loadBalancer = require('./services/loadBalancer');
    loadBalancer.start();
    console.log('✅ Load balancer started successfully');

  } catch (error) {
    console.error('❌ Error initializing services:', error);
    // Don't exit process, but log the error for monitoring
  }
});